import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { ApiService } from './api.service';
import { ApiResponse } from '../models/api-response.model';
import { BranchStore } from './menu.service';
import moment from 'moment';
import { environment } from 'src/environments/environment';

// Interface cho request đặt bàn theo API thực tế
export interface BookingRequest {
  orderId: string;
  userId: string;
  storeId: string;
  storeManagerId: string;
  customerName: string;
  phone: string;
  note?: string;
  branchId: string;
  branchName: string;
  branchAddress: string;
  price: number;
  branchPhone: string;
  reservationDate: string;
  reservationTime: string;
  numberOfGuests: number;
  specialRequirements?: string;
  occasion?: string;
  isPayOnline: number;
  paymentMethod: number;
  typeBooking: number;
  status: number;
  items: BookingItem[];
}

export interface BookingItem {
  serviceName: string;
  price: number;
  numberOfGuests: number;
  reservationDate: string;
  reservationTime: string;
}

export interface BookingResponse {
  success: boolean;
  message: string;
  data?: any;
}

// Interface cho form input từ user
export interface BookingFormData {
  branchId: string;
  customerName: string;
  phoneNumber: string;
  email: string;
  reservationDate: Date | string; // Hỗ trợ cả Date và string từ datetime picker
  adultCount: number;
  childCount: number;
  mealType: string;
  notes?: string;
}

export interface BranchOption {
  value: string;
  label: string;
  address?: string;
  phone?: string;
}

export interface MealOption {
  value: string;
  label: string;
  description?: string;
}

@Injectable({
  providedIn: 'root',
})
export class BookingService {
  // Thông tin cố định từ API
  private readonly defaultStoreId = environment.storeId;
  private readonly defaultStoreManagerId = '6320565fb33a347a94aa987c';
  private readonly defaultUserId = 'GUEST';

  constructor(private apiService: ApiService) {}

  /**
   * Tạo đặt bàn mới theo API thực tế
   * @param formData Dữ liệu từ form
   * @param selectedBranch Thông tin chi nhánh đã chọn
   */
  createBooking(
    formData: BookingFormData,
    selectedBranch: BranchStore,
  ): Observable<ApiResponse<BookingResponse>> {
    const orderId = this.generateOrderId();
    const reservationDateTime = this.formatDateTime(formData.reservationDate);
    const totalGuests = formData.adultCount + formData.childCount;

    const bookingRequest: BookingRequest = {
      orderId: orderId,
      userId: this.defaultUserId,
      storeId: this.defaultStoreId,
      storeManagerId: this.defaultStoreManagerId,
      customerName: formData.customerName,
      phone: formData.phoneNumber,
      note: formData.notes || '',
      branchId: formData.branchId,
      branchName: selectedBranch.name,
      branchAddress: selectedBranch.address,
      price: 0,
      branchPhone: selectedBranch.phone || selectedBranch.hotline || '',
      reservationDate: reservationDateTime.date,
      reservationTime: reservationDateTime.time,
      numberOfGuests: totalGuests,
      specialRequirements: formData.notes || '',
      occasion: this.getMealTypeDescription(formData.mealType),
      isPayOnline: 0,
      paymentMethod: 0,
      typeBooking: 5, // Đặt bàn nhà hàng
      status: 0,
      items: [
        {
          serviceName: 'Đặt bàn nhà hàng',
          price: 0,
          numberOfGuests: totalGuests,
          reservationDate: reservationDateTime.date,
          reservationTime: reservationDateTime.time,
        },
      ],
    };

    // Sử dụng ApiService để gọi API public
    return this.apiService.post<BookingResponse>(
      'user/api/booking-show-room-guest?output=json',
      bookingRequest,
    );
  }

  /**
   * Lấy danh sách chi nhánh từ API getBranchStores
   * @param storeId ID của store
   */
  getBranches(storeId: string = this.defaultStoreId): Observable<BranchOption[]> {
    return this.apiService.get<any>(`user/api/get-branch-stores/${storeId}`).pipe(
      map(response => {
        if (response && response.data && response.data.branchStore) {
          return response.data.branchStore.map((branch: BranchStore) => ({
            value: branch._id,
            label: branch.name,
            address: branch.address,
            phone: branch.phone || branch.hotline,
          }));
        }
        return [];
      }),
    );
  }

  /**
   * Lấy danh sách loại suất ăn từ API services-of-brand
   * @param categoryId ID của danh mục (optional, default: lấy tất cả)
   */
  getMealTypes(categoryId?: string): Observable<MealOption[]> {
    let endpoint = `user/api/services-of-brand/${environment.storeId}/show-room`;

    // Thêm categoryId vào query params nếu có
    if (categoryId && categoryId !== 'all') {
      endpoint += `?categoryId=${categoryId}`;
    }

    console.log('🔥 Calling getMealTypes API:', endpoint);

    return this.apiService.get<any>(endpoint).pipe(
      map((response: any) => {
        console.log('🔥 API Response:', response);

        // Response structure: response.data.data (nested data)
        if (response && response.data && response.data.data && Array.isArray(response.data.data)) {
          const services = response.data.data.map((service: any) => ({
            ...service,
            value: service._id,
            label: service.name,
            description: service.description || service.shortDes || '',
            price: service.price || 0,
          }));
          console.log('🔥 Mapped services:', services);
          return services;
        }

        console.log('🔥 API response invalid or empty data');
        return [];
      }),
      catchError(error => {
        console.error('🔥 API Error:', error);
        return of([]);
      }),
    );
  }

  /**
   * Tạo order ID ngẫu nhiên
   */
  private generateOrderId(): string {
    return Date.now().toString() + Math.random().toString(36).substring(2, 7);
  }

  /**
   * Format datetime thành date và time riêng biệt
   * Hỗ trợ cả Date object và string từ datetime picker
   */
  private formatDateTime(dateInput: Date | string): { date: string; time: string } {
    let date: Date;

    if (typeof dateInput === 'string') {
      // Parse string từ datetime picker (format: DD/MM/YYYY HH:mm)
      const parsed = moment(dateInput, 'DD/MM/YYYY HH:mm');
      if (parsed.isValid()) {
        date = parsed.toDate();
      } else {
        // Fallback: try other common formats
        date = new Date(dateInput);
      }
    } else {
      date = dateInput;
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return {
      date: `${year}-${month}-${day}`,
      time: `${hours}:${minutes}`,
    };
  }

  /**
   * Lấy mô tả loại suất ăn
   */
  private getMealTypeDescription(mealType: string): string {
    const mealTypes: { [key: string]: string } = {
      breakfast: 'Bữa sáng',
      lunch: 'Bữa trưa',
      dinner: 'Bữa tối',
      buffet: 'Buffet',
    };
    return mealTypes[mealType] || 'Đặt bàn';
  }
}
