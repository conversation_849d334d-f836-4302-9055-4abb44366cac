import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { catchError, filter, take, switchMap, finalize } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

@Injectable()
export class TokenInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);

  constructor() {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    // Không thêm token cho các request tới các API khác hoặc các endpoint đăng nhập/đăng ký
    if (
      !request.url.includes(environment.apiUrl) ||
      request.url.includes('auth/login') ||
      request.url.includes('auth/register')
    ) {
      return next.handle(request);
    }

    // Thêm token vào header nếu có
    const token = localStorage.getItem(environment.tokenKey);
    if (token) {
      request = this.addTokenHeader(request, token);
    }

    return next.handle(request).pipe(
      catchError(error => {
        if (error instanceof HttpErrorResponse && error.status === 401) {
          return this.handle401Error(request, next);
        }
        return throwError(() => error);
      }),
    );
  }

  private addTokenHeader(request: HttpRequest<any>, token: string) {
    return request.clone({
      headers: request.headers.set('access-token', token),
    });
  }

  private handle401Error(request: HttpRequest<any>, next: HttpHandler) {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      const refreshToken = localStorage.getItem(environment.refreshTokenKey);

      // Nếu có refresh token, gọi API refresh token
      if (refreshToken) {
        // Trong thực tế, chúng ta sẽ gọi service để refresh token
        // Ở đây tạm thời chúng ta throw error để client tự logout
        return throwError(() => new Error('Token expired'));

        // Code cho việc refresh token có thể trông như thế này:
        /*
        return this.authService.refreshToken().pipe(
          switchMap((token: any) => {
            this.isRefreshing = false;
            this.refreshTokenSubject.next(token.accessToken);

            // Lưu token mới vào localStorage
            localStorage.setItem(environment.tokenKey, token.accessToken);
            localStorage.setItem(environment.refreshTokenKey, token.refreshToken);

            // Gửi lại request với token mới
            return next.handle(this.addTokenHeader(request, token.accessToken));
          }),
          catchError((err) => {
            this.isRefreshing = false;
            // Logout user nếu refresh token thất bại
            this.authService.logout();
            return throwError(() => err);
          }),
          finalize(() => {
            this.isRefreshing = false;
          })
        );
        */
      }

      return throwError(() => new Error('No refresh token'));
    }

    // Chờ khi refresh token thành công
    return this.refreshTokenSubject.pipe(
      filter(token => token !== null),
      take(1),
      switchMap(token => next.handle(this.addTokenHeader(request, token))),
    );
  }
}
