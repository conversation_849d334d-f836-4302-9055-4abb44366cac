export interface CartItem {
  productId: string;
  count: number;
  shopId: string;
  classifyActive?: ClassifyOption;
  noteProduct: string;
  info?: ProductInfo;
  selected?: boolean;
}

export interface ClassifyOption {
  _id: string;
  name: string;
  value: string;
  price: string;
  checked: boolean;
}

export interface ProductInfo {
  _id: string;
  name: string;
  price: number;
  priceOld?: number;
  thumbail: string;
  weight: number;
  description?: string;
  pictures?: string[];
  classify?: ProductClassify[];
  storeName?: string;
  storeId?: string;
  revenue?: number;
  watched?: number;
}

export interface ProductClassify {
  name: string;
  data: ClassifyOption[];
}

export interface ShopCart {
  _id: string;
  info: ShopInfo;
  selected: boolean;
  haveItem: boolean;
  totalPriceShop: number;
  shopCount: number;
  products: CartItem[];
}

export interface ShopInfo {
  _id: string;
  name: string;
  address: string;
  phone?: string;
}

export interface CartSummary {
  totalPrice: number;
  totalCount: number;
  selectedItems: CartItem[];
  selectedShops: ShopCart[];
}

export interface OrderData {
  orderId?: string;
  userId: string;
  storeId: string;
  products: CartItem[];
  name: string; // Họ và tên - required by backend
  phone: string;
  province: string;
  district: string;
  ward: string;
  street: string;
  location: string;
  payment: number; // 0: online, 1: cash
  bankCode: string;
  couponCode?: string;
  totalAmount: number;
  transportFee?: number;
  shippingService?: string;
}

export interface CouponData {
  code: string;
  discount: number;
  discountType: number; // 0: %, 1: VND
}

export interface CouponResponse {
  coupon: CouponData;
  totalPrice: number;
  discount: number;
  rsCoupon: {
    error: boolean;
    msg: string;
  };
}

export interface BranchStore {
  _id: string;
  name: string;
  address: string;
  phone: string;
  storeId: string;
}

export interface CustomerInfo {
  _id: string;
  name: string;
  phone: string;
  address: string;
  province?: string;
  district?: string;
  ward?: string;
  street?: string;
  default?: boolean; // Địa chỉ mặc định
}

export interface PaymentMethod {
  _id: string;
  name: string;
  description: string;
  type: 'cash' | 'bank';
}

export interface BankInfo {
  _id: string;
  name: string;
  code: string;
  logo: string;
}

// Local storage keys
export const CART_STORAGE_KEY = 'lbvd_cart';
export const SELECTED_STORE_KEY = 'lbvd_selected_store';
export const CUSTOMER_INFO_KEY = 'lbvd_customer_info';

// Constants
export const BRAND_ID = '623a9950be781e0ba814f22a';
export const PAYMENT_METHODS: PaymentMethod[] = [
  {
    _id: '1',
    name: 'Chuyển khoản ngân hàng',
    description: 'Thanh toán qua chuyển khoản',
    type: 'bank',
  },
  {
    _id: '2',
    name: 'Thanh toán tiền mặt',
    description: 'Thanh toán bằng tiền mặt khi nhận hàng',
    type: 'cash',
  },
];

export const BANKS: BankInfo[] = [
  {
    _id: '1',
    name: 'Vietcombank',
    code: 'VCB',
    logo: 'assets/images/banks/vcb.png',
  },
  {
    _id: '2',
    name: 'Techcombank',
    code: 'TCB',
    logo: 'assets/images/banks/tcb.png',
  },
  {
    _id: '3',
    name: 'BIDV',
    code: 'BIDV',
    logo: 'assets/images/banks/bidv.png',
  },
  {
    _id: '4',
    name: 'Vietinbank',
    code: 'VTB',
    logo: 'assets/images/banks/vtb.png',
  },
];
