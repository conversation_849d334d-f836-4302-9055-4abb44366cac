import { Address } from './address.model';

export interface LoginRequest {
  phone: string;
  password: string;
}

export interface RegisterRequest {
  fullName: string;
  password: string;
  phone: string;
}

export interface AuthResponse {
  // Direct API response fields
  error: boolean;
  message: string;
  data?: {
    user: UserModel;
    token: string;
  };
}

export interface UserModel {
  _id: string;
  fullName: string;
  email?: string;
  phone?: string;
  picture?: string;
  status: number;
  confirmPhone?: number;
  confirmEmail?: number;
  createAt?: number;
  modifyAt?: number;
  birthday?: number;
  address?: string;
  location?: string;
  street?: string;
  userName?: string;
  wallet?: number;
  funds?: number;
  point?: number;
  type?: number;
  code?: number;
  addressList?: Address[];
  servicesGas?: number;
  servicesShowroom?: number;
  servicesParking?: number;
  servicesSpa?: number;
  servicesHotel?: number;
  servicesExamination?: number;
  servicesStore?: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
  statusCode: number;
  success: boolean;
}
