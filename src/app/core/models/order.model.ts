export interface OrderProduct {
  _id: string;
  productId: string;
  name: string;
  price: number;
  count: number;
  weight?: number;
  thumbnail?: string;
  classifyActive?: {
    _id: string;
    name: string;
    value: string;
    price: string;
    mass?: string;
  };
  info?: {
    name: string;
    price: number;
    thumbail?: string;
    weight?: number;
  };
  noteProduct?: string;
}

export interface OrderStore {
  _id: string;
  name: string;
  address: string;
  phone?: string;
}

export interface OrderHistory {
  _id: string;
  orderId: string;
  userId: string;
  storeId: string;
  store?: OrderStore;
  products: OrderProduct[];
  name: string; // Customer name
  phone: string;
  province: string;
  district: string;
  ward: string;
  street: string;
  location: string; // Full address
  note?: string;
  paymentMethod: number; // 0: Cash, 1: Online, 2: Points
  bankCode?: string;
  totalAmount: number;
  transportFee: number;
  couponCode?: string;
  couponDiscount?: number;
  status: number; // Order status
  statusText?: string;
  createdAt: string;
  updatedAt: string;
  bookingType: number; // 0: Product order
  shippingService?: string;
  trackingCode?: string;
}

export interface OrderHistoryResponse {
  error: boolean;
  message: string;
  data: {
    orders: OrderHistory[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface OrderDetailResponse {
  error: boolean;
  message: string;
  data: OrderHistory;
}

// Order status mapping
export const ORDER_STATUS = {
  0: 'Chờ xác nhận',
  1: 'Đã xác nhận',
  2: 'Đang chuẩn bị',
  3: 'Đang giao hàng',
  4: 'Đã giao hàng',
  5: 'Đã hủy',
  6: 'Hoàn trả',
  7: 'Chờ thanh toán'
} as const;

// Payment method mapping
export const PAYMENT_METHOD = {
  0: 'Tiền mặt',
  1: 'Thanh toán online',
  2: 'Điểm tích lũy'
} as const;

// Order status colors
export const ORDER_STATUS_COLORS = {
  0: '#ffc107', // Warning - Chờ xác nhận
  1: '#17a2b8', // Info - Đã xác nhận
  2: '#fd7e14', // Orange - Đang chuẩn bị
  3: '#007bff', // Primary - Đang giao hàng
  4: '#28a745', // Success - Đã giao hàng
  5: '#dc3545', // Danger - Đã hủy
  6: '#6c757d', // Secondary - Hoàn trả
  7: '#e83e8c'  // Pink - Chờ thanh toán
} as const;
