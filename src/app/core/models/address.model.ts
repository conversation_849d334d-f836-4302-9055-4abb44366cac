export interface Address {
  _id?: string;
  name: string;
  phone: string;
  province: string;
  district: string;
  ward: string;
  street: string;
  address: string; // Full address string
  default: boolean;
}

export interface Province {
  _id: string;
  name: string;
  code: string;
}

export interface District {
  _id: string;
  name: string;
  code: string;
  provinceCode: string;
}

export interface Ward {
  _id: string;
  name: string;
  code: string;
  districtCode: string;
}

export interface AddressFormData {
  name: string;
  phone: string;
  province: string;
  district: string;
  ward: string;
  street: string;
  default?: boolean;
}

export interface AddressApiResponse {
  error: boolean;
  message: string;
  data?: any;
}

// Constants for validation
export const ADDRESS_VALIDATION = {
  NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 100,
    REQUIRED: true
  },
  PHONE: {
    MIN_LENGTH: 10,
    MAX_LENGTH: 15,
    REQUIRED: true,
    PATTERN: /^[0-9+\-\s()]+$/
  },
  PROVINCE: {
    REQUIRED: true
  },
  DISTRICT: {
    REQUIRED: true
  },
  WARD: {
    REQUIRED: true
  },
  STREET: {
    MIN_LENGTH: 5,
    MAX_LENGTH: 200,
    REQUIRED: true
  }
};

// Helper functions
export class AddressHelper {
  /**
   * Build full address string from components
   */
  static buildFullAddress(street: string, ward: string, district: string, province: string): string {
    const parts = [street, ward, district, province].filter(part => part && part.trim());
    return parts.join(', ');
  }

  /**
   * Validate address form data
   */
  static validateAddress(address: AddressFormData): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate name
    if (!address.name || address.name.trim().length < ADDRESS_VALIDATION.NAME.MIN_LENGTH) {
      errors.push(`Tên phải có ít nhất ${ADDRESS_VALIDATION.NAME.MIN_LENGTH} ký tự`);
    }
    if (address.name && address.name.length > ADDRESS_VALIDATION.NAME.MAX_LENGTH) {
      errors.push(`Tên không được vượt quá ${ADDRESS_VALIDATION.NAME.MAX_LENGTH} ký tự`);
    }

    // Validate phone
    if (!address.phone || address.phone.trim().length < ADDRESS_VALIDATION.PHONE.MIN_LENGTH) {
      errors.push(`Số điện thoại phải có ít nhất ${ADDRESS_VALIDATION.PHONE.MIN_LENGTH} ký tự`);
    }
    if (address.phone && !ADDRESS_VALIDATION.PHONE.PATTERN.test(address.phone)) {
      errors.push('Số điện thoại không đúng định dạng');
    }

    // Validate location fields
    if (!address.province) {
      errors.push('Vui lòng chọn tỉnh/thành phố');
    }
    if (!address.district) {
      errors.push('Vui lòng chọn quận/huyện');
    }
    if (!address.ward) {
      errors.push('Vui lòng chọn phường/xã');
    }

    // Validate street
    if (!address.street || address.street.trim().length < ADDRESS_VALIDATION.STREET.MIN_LENGTH) {
      errors.push(`Địa chỉ cụ thể phải có ít nhất ${ADDRESS_VALIDATION.STREET.MIN_LENGTH} ký tự`);
    }
    if (address.street && address.street.length > ADDRESS_VALIDATION.STREET.MAX_LENGTH) {
      errors.push(`Địa chỉ cụ thể không được vượt quá ${ADDRESS_VALIDATION.STREET.MAX_LENGTH} ký tự`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Format phone number
   */
  static formatPhoneNumber(phone: string): string {
    if (!phone) return '';
    
    // Remove all non-digit characters except +
    let cleaned = phone.replace(/[^\d+]/g, '');
    
    // Handle +840 -> +84
    if (cleaned.startsWith('+840')) {
      cleaned = cleaned.replace('+840', '+84');
    }
    
    return cleaned;
  }

  /**
   * Check if address is complete
   */
  static isAddressComplete(address: Partial<Address>): boolean {
    return !!(
      address.name &&
      address.phone &&
      address.province &&
      address.district &&
      address.ward &&
      address.street
    );
  }

  /**
   * Create address object from form data
   */
  static createAddressFromForm(formData: AddressFormData): Address {
    const fullAddress = this.buildFullAddress(
      formData.street,
      formData.ward,
      formData.district,
      formData.province
    );

    return {
      name: formData.name.trim(),
      phone: this.formatPhoneNumber(formData.phone),
      province: formData.province,
      district: formData.district,
      ward: formData.ward,
      street: formData.street.trim(),
      address: fullAddress,
      default: formData.default || false
    };
  }

  /**
   * Compare two addresses for equality
   */
  static areAddressesEqual(addr1: Address, addr2: Address): boolean {
    return (
      addr1.name === addr2.name &&
      addr1.phone === addr2.phone &&
      addr1.province === addr2.province &&
      addr1.district === addr2.district &&
      addr1.ward === addr2.ward &&
      addr1.street === addr2.street
    );
  }
}
