<section class="container tin-tuc-section">
  <div class="tin-tuc-label">
    <span>TIN TỨC</span>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="loading" class="loading-container">
    <div class="spinner-container">
      <div class="spinner"></div>
      <span><PERSON><PERSON> tải...</span>
    </div>
  </div>

  <!-- News list -->
  <div *ngIf="!loading" class="news-list-container">
    <div class="news-list">
      <div *ngFor="let news of newsList" class="news-item">
        <div class="news-image-container">
          <img [src]="news.thumbail || 'assets/images/placeholder.jpg'" class="news-image" alt="{{ news.title }}" (error)="handleImageError($event)" />
        </div>
        <div class="news-content">
          <a [routerLink]="['/chi-tiet', news._id]" class="news-title">{{ news.title }}</a>
          <p class="news-description">{{ news.description }}</p>
          <div class="news-meta">
            <span class="news-date">Ngày đăng: {{ news.createdAt | date: 'dd/MM/yyyy' }}</span>
            <span class="news-views">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              {{ news.views || 0 }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty state -->
    <div *ngIf="newsList.length === 0" class="empty-news-state">
      <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
        <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <p>Không có tin tức nào.</p>
    </div>
  </div>
</section>
