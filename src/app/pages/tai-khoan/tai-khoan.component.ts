import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../core/services/auth.service';
import { UserModel } from '../../core/models/auth.model';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar } from '@angular/material/snack-bar';
import { RouterLink } from '@angular/router';
import { TablerIconsModule } from 'angular-tabler-icons';
import { MatButton } from '@angular/material/button';
import { AddressManagementComponent } from '../../components/address-management/address-management.component';

@Component({
  selector: 'app-tai-khoan',
  templateUrl: './tai-khoan.component.html',
  styleUrls: ['./tai-khoan.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatButton,
    TablerIconsModule,
    AddressManagementComponent,
  ],
})
export class TaiKhoanComponent implements OnInit {
  user: UserModel | null = null;
  isLoading = false;
  error: string | null = null;

  constructor(private authService: AuthService, private snackBar: MatSnackBar) {}

  ngOnInit(): void {
    this.loadUserData();
  }

  loadUserData(): void {
    this.isLoading = true;
    this.error = null;

    this.authService.checkToken().subscribe({
      next: response => {
        if (!response.error && response.data?.user) {
          this.user = response.data.user;
        } else {
          this.error = response.message || 'Không thể tải thông tin người dùng';
          this.snackBar.open(this.error, 'Đóng', {
            duration: 5000,
            horizontalPosition: 'center',
            verticalPosition: 'bottom',
            panelClass: ['snackbar-error'],
          });
        }
        this.isLoading = false;
      },
      error: error => {
        console.error('Error loading user data:', error);
        this.error = 'Đã xảy ra lỗi khi tải thông tin tài khoản';
        this.snackBar.open(this.error, 'Đóng', {
          duration: 5000,
          horizontalPosition: 'center',
          verticalPosition: 'bottom',
          panelClass: ['snackbar-error'],
        });
        this.isLoading = false;
      },
    });
  }

  /**
   * Xử lý trường hợp ảnh avatar bị lỗi
   * Thay thế bằng vòng tròn màu xám
   */
  handleImageError(event: Event): void {
    const imgElement = event.target as HTMLImageElement;
    imgElement.style.display = 'none'; // Ẩn hình ảnh lỗi

    // Tìm parent element (avatar-wrapper) và thêm class để hiển thị hình tròn xám
    const parentElement = imgElement.parentElement;
    if (parentElement) {
      parentElement.classList.add('avatar-placeholder');
    }
  }
}
