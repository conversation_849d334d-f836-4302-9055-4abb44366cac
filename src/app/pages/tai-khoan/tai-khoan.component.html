<div class="container py-4">
  <div class="row justify-content-center">
    <div class="col-lg-8 col-md-10 col-12">
      <mat-card>
        <div class="card-header d-flex justify-content-between align-items-center p-4">
          <h2 class="m-0">Thông tin tài k<PERSON>n</h2>
          <button mat-button color="primary" class="reload-btn" (click)="loadUserData()" [disabled]="isLoading">
            <i-tabler name="refresh" [class.rotating]="isLoading"></i-tabler>
          </button>
        </div>
        
        <mat-divider></mat-divider>
        
        <div class="p-4">
          <!-- Loading spinner -->
          <div class="text-center my-5" *ngIf="isLoading">
            <mat-spinner diameter="40" class="mx-auto"></mat-spinner>
            <p class="mt-3"><PERSON><PERSON> tải thông tin...</p>
          </div>
          
          <!-- Error message -->
          <div class="alert alert-danger" *ngIf="error && !isLoading">
            <i-tabler name="alert-circle" class="me-2"></i-tabler>
            {{ error }}
          </div>
          
          <!-- User information -->
          <div class="user-info" *ngIf="user && !isLoading">
            <div class="text-center mb-4">
              <div class="avatar-wrapper mx-auto">
                <img [src]="user.picture || '/template/default-avatar.jpg'" alt="Avatar" class="user-avatar" (error)="handleImageError($event)">
              </div>
              <h3 class="mt-3">{{ user.fullName }}</h3>
              <p class="text-muted">ID: {{ user._id }}</p>
            </div>
            
            <mat-divider class="my-3"></mat-divider>
            
            <div class="info-section">
              <h4>Thông tin cá nhân</h4>
              
              <div class="info-item">
                <span class="info-label">Họ và tên:</span>
                <span class="info-value">{{ user.fullName }}</span>
              </div>
              
              <div class="info-item">
                <span class="info-label">Số điện thoại:</span>
                <span class="info-value">{{ user.phone }}</span>
                <span class="status-badge" [ngClass]="user.confirmPhone ? 'confirmed' : 'unconfirmed'">
                  {{ user.confirmPhone ? 'Đã xác thực' : 'Chưa xác thực' }}
                </span>
              </div>
              
              <div class="info-item">
                <span class="info-label">Email:</span>
                <span class="info-value">{{ user.email || 'Chưa cập nhật' }}</span>
                <span class="status-badge" *ngIf="user.email" [ngClass]="user.confirmEmail ? 'confirmed' : 'unconfirmed'">
                  {{ user.confirmEmail ? 'Đã xác thực' : 'Chưa xác thực' }}
                </span>
              </div>
              
              <div class="info-item">
                <span class="info-label">Địa chỉ:</span>
                <span class="info-value">{{ user.address || 'Chưa cập nhật' }}</span>
              </div>
              
              <!-- <div class="info-item">
                <span class="info-label">Điểm:</span>
                <span class="info-value">{{ user.point || 0 }}</span>
              </div>
              
              <div class="info-item">
                <span class="info-label">Ví:</span>
                <span class="info-value">{{ user.wallet | number }} VNĐ</span>
              </div> -->
            </div>
            
            <mat-divider class="my-3"></mat-divider>
            
            <div class="info-section">
              <!-- <h4>Dịch vụ đã đăng ký</h4> -->
              
              <!-- <div class="services-grid">
                <div class="service-item" [class.active]="user.servicesGas">
                  <i-tabler name="gas-station" class="service-icon"></i-tabler>
                  <span>Gas</span>
                </div>
                
                <div class="service-item" [class.active]="user.servicesShowroom">
                  <i-tabler name="car" class="service-icon"></i-tabler>
                  <span>Showroom</span>
                </div>
                
                <div class="service-item" [class.active]="user.servicesParking">
                  <i-tabler name="parking" class="service-icon"></i-tabler>
                  <span>Bãi đỗ xe</span>
                </div>
                
                <div class="service-item" [class.active]="user.servicesSpa">
                  <i-tabler name="massage" class="service-icon"></i-tabler>
                  <span>Spa</span>
                </div>
                
                <div class="service-item" [class.active]="user.servicesHotel">
                  <i-tabler name="building" class="service-icon"></i-tabler>
                  <span>Khách sạn</span>
                </div>
                
                <div class="service-item" [class.active]="user.servicesExamination">
                  <i-tabler name="stethoscope" class="service-icon"></i-tabler>
                  <span>Khám bệnh</span>
                </div>
                
                <div class="service-item" [class.active]="user.servicesStore">
                  <i-tabler name="shopping-cart" class="service-icon"></i-tabler>
                  <span>Cửa hàng</span>
                </div>
              </div> -->
            </div>
            
            <!-- <div class="text-center mt-4">
              <button mat-button color="primary" routerLink="/doi-thong-tin">
                <i-tabler name="edit" class="me-2"></i-tabler>
                Chỉnh sửa thông tin
              </button>
            </div> -->
          </div>
        </div>
      </mat-card>

      <!-- Address Management Section -->
      <div *ngIf="user && !isLoading">
        <app-address-management></app-address-management>
      </div>
    </div>
  </div>
</div>
