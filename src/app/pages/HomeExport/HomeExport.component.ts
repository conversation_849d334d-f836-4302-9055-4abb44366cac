import { Component, ViewEncapsulation, HostBinding, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BannerService, Banner } from '../../core/services/banner.service';
import { Subscription } from 'rxjs';
import { LoadingService } from '../../components/loading/loading.service';

@Component({
  selector: 'home-export',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [CommonModule],
  templateUrl: './HomeExport.component.html',
  styleUrls: ['./HomeExport.component.scss'],
})
export class HomeExport implements OnInit, OnDestroy {
  @HostBinding('style.display') display = 'contents';

  banners: Banner[] = [];
  isLoading: boolean = true;
  error: string | null = null;
  private subscription: Subscription = new Subscription();

  constructor(private bannerService: BannerService, private loadingService: LoadingService) {}

  ngOnInit(): void {
    this.loadBanners();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions to prevent memory leaks
    this.subscription.unsubscribe();
  }

  loadBanners(): void {
    this.isLoading = true;
    this.error = null;

    // Show global loading indicator
    this.loadingService.show();

    const bannerSub = this.bannerService.getBanners().subscribe({
      next: response => {
        this.isLoading = false;
        // Hide global loading indicator
        this.loadingService.hide();

        if (!response.error && response.data.banners) {
          this.banners = response.data.banners;
          console.log('Banners loaded:', this.banners);
        } else {
          this.error = response.message || 'Không thể tải banner';
          console.error('Error in banner response:', response);
        }
      },
      error: err => {
        this.isLoading = false;
        // Hide global loading indicator on error
        this.loadingService.hide();

        this.error = 'Đã xảy ra lỗi khi tải banner';
        console.error('Banner loading error:', err);
      },
    });

    this.subscription.add(bannerSub);
  }

  getBannerImageUrl(banner: Banner): string {
    // Use Google Cloud Storage URL as the base for banner images
    const baseUrl = 'https://storage.googleapis.com/lnbvd-75473.firebasestorage.app';
    return banner.thumbail.startsWith('http')
      ? encodeURIComponent(banner.thumbail)
      : `${baseUrl}${encodeURIComponent(banner.thumbail)}`;
  }

  onImageClick(banner: Banner) {
    // Handle banner click based on screen type
    console.log('Banner clicked:', banner);

    // Basic navigation logic based on banner screen type
    if (banner.screen === 'LINK' && banner.params) {
      window.open(banner.params, '_blank');
    }
    // Additional navigation logic can be added here
  }
}
