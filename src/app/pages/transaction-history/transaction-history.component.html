<div class="container order-history-container">
  <!-- Header -->
  <div class="page-header">
    <h2 class="page-title">
      <i-tabler name="history" class="title-icon"></i-tabler>
      <PERSON><PERSON><PERSON> sử đặt hàng
    </h2>
    <button mat-icon-button (click)="refreshOrders()" [disabled]="loading" class="refresh-button">
      <i-tabler name="refresh" [class.spinning]="loading"></i-tabler>
    </button>
  </div>

  <!-- Loading state -->
  <div *ngIf="loading && orders.length === 0" class="loading-state">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Đang tải lịch sử đơn hàng...</p>
  </div>

  <!-- Order list -->
  <div class="order-list" *ngIf="!loading || orders.length > 0">
    <div class="order-item" *ngFor="let order of orders; trackBy: trackByOrderId" (click)="openOrderDetail(order)">
      <!-- Order header -->
      <div class="order-header">
        <div class="order-id">
          <i-tabler name="receipt" class="order-icon"></i-tabler>
          <span class="order-number">{{ order.orderId }}</span>
        </div>
        <span
          class="status-badge"
          [style.background-color]="getStatusColor(order.status)">
          {{ order.statusText }}
        </span>
      </div>

      <!-- Order details -->
      <div class="order-details">
        <div class="detail-row">
          <i-tabler name="credit-card" class="detail-icon"></i-tabler>
          <span class="detail-label">Thanh toán:</span>
          <span class="detail-value">{{ getPaymentMethodText(order.paymentMethod) }}</span>
        </div>
        <div class="detail-row">
          <i-tabler name="calendar" class="detail-icon"></i-tabler>
          <span class="detail-label">Ngày đặt:</span>
          <span class="detail-value">{{ order.createdAt | date:'dd/MM/yyyy HH:mm' }}</span>
        </div>
        <div class="detail-row">
          <i-tabler name="currency-dong" class="detail-icon"></i-tabler>
          <span class="detail-label">Tổng tiền:</span>
          <span class="detail-value total-amount">{{ formatPrice(order.totalAmount) }}</span>
        </div>
        <div class="detail-row" *ngIf="order.store">
          <i-tabler name="building-store" class="detail-icon"></i-tabler>
          <span class="detail-label">Cửa hàng:</span>
          <span class="detail-value">{{ order.store.name }}</span>
        </div>
      </div>

      <!-- Order products preview -->
      <div class="order-products-preview" *ngIf="order.products && order.products.length > 0">
        <div class="products-summary">
          <i-tabler name="package" class="products-icon"></i-tabler>
          <span class="products-count">{{ order.products.length }} sản phẩm</span>
          <span class="products-names">{{ getProductsPreview(order.products) }}</span>
        </div>
      </div>

      <!-- Order actions -->
      <div class="order-actions" (click)="$event.stopPropagation()">
        <button
          mat-stroked-button
          color="primary"
          (click)="openOrderDetail(order)"
          class="detail-button">
          <i-tabler name="eye" class="button-icon"></i-tabler>
          Chi tiết
        </button>
      </div>
    </div>

    <!-- Load more button -->
    <div class="load-more-container" *ngIf="hasMoreData">
      <button
        mat-raised-button
        color="primary"
        (click)="loadMoreOrders()"
        [disabled]="loading"
        class="load-more-button">
        <mat-spinner diameter="20" *ngIf="loading" class="button-spinner"></mat-spinner>
        <i-tabler name="chevron-down" *ngIf="!loading" class="button-icon"></i-tabler>
        {{ loading ? 'Đang tải...' : 'Tải thêm' }}
      </button>
    </div>

    <!-- Empty state -->
    <div *ngIf="orders.length === 0 && !loading" class="empty-state">
      <i-tabler name="shopping-cart-off" class="empty-icon"></i-tabler>
      <h3>Chưa có đơn hàng nào</h3>
      <p>Bạn chưa có đơn hàng nào. Hãy bắt đầu mua sắm ngay!</p>
      <button mat-raised-button color="primary" routerLink="/home" class="shop-now-button">
        <i-tabler name="shopping-cart" class="button-icon"></i-tabler>
        Mua sắm ngay
      </button>
    </div>
  </div>
</div>
