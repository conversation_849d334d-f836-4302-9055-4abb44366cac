import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { TablerIconsModule } from 'angular-tabler-icons';
import { Subject } from 'rxjs';
import { finalize, takeUntil } from 'rxjs/operators';

import { OrderHistory, ORDER_STATUS_COLORS } from '../../core/models/order.model';
import { OrderHistoryService } from '../../core/services/order-history.service';
import { OrderDetailDialogComponent } from '../../components/order-detail-dialog/order-detail-dialog.component';
import { LoadingService } from '../../components/loading/loading.service';

@Component({
  selector: 'app-transaction-history',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    TablerIconsModule,
  ],
  templateUrl: './transaction-history.component.html',
  styleUrls: ['./transaction-history.component.scss'],
})
export class TransactionHistoryComponent implements OnInit, OnDestroy {
  orders: OrderHistory[] = [];
  loading = false;
  currentPage = 1;
  pageSize = 10;
  totalPages = 0;
  hasMoreData = true;

  private destroy$ = new Subject<void>();

  constructor(
    private orderHistoryService: OrderHistoryService,
    private loadingService: LoadingService,
    private dialog: MatDialog,
  ) {}

  ngOnInit() {
    this.loadOrderHistory();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load order history from API
   */
  private loadOrderHistory(page: number = 1, append: boolean = false): void {
    this.loading = true;
    this.loadingService.show();
    this.orderHistoryService
      .getOrderHistory(page, this.pageSize)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.loading = false;
          this.loadingService.hide();
        }),
      )
      .subscribe({
        next: response => {
          this.loading = false;

          if (!response.error) {
            if (append) {
              this.orders = [...this.orders, ...response.data.orders];
            } else {
              this.orders = response.data.orders;
            }

            this.currentPage = response.data.page;
            this.totalPages = response.data.totalPages;
            this.hasMoreData = this.currentPage < this.totalPages;

            console.log('📋 Orders loaded:', this.orders.length, 'total pages:', this.totalPages);
          } else {
            console.error('Error loading orders:', response.message);
          }
        },
        error: error => {
          this.loading = false;
          this.loadingService.hide();
          console.error('Error loading orders:', error);
        },
      });
  }

  /**
   * Load more orders (pagination)
   */
  loadMoreOrders(): void {
    if (this.hasMoreData && !this.loading) {
      this.loadOrderHistory(this.currentPage + 1, true);
    }
  }

  /**
   * Refresh order list
   */
  refreshOrders(): void {
    this.currentPage = 1;
    this.hasMoreData = true;
    this.loadOrderHistory();
  }

  /**
   * Open order detail dialog
   */
  openOrderDetail(order: OrderHistory): void {
    const dialogRef = this.dialog.open(OrderDetailDialogComponent, {
      width: '90vw',
      maxWidth: '800px',
      maxHeight: '90vh',
      data: { orderId: order.orderId }, // Use orderId for API call
      panelClass: 'order-detail-dialog-container',
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result?.action === 'reorder') {
        // Handle reorder action
        console.log('Reorder requested for:', result.order);
      }
    });
  }

  /**
   * Get status color for order
   */
  getStatusColor(status: number): string {
    return ORDER_STATUS_COLORS[status as keyof typeof ORDER_STATUS_COLORS] || '#6c757d';
  }

  /**
   * Get payment method text
   */
  getPaymentMethodText(paymentMethod: number): string {
    return this.orderHistoryService.getPaymentMethodText(paymentMethod);
  }

  /**
   * Format price to VND
   */
  formatPrice(price: number): string {
    return this.orderHistoryService.formatPrice(price);
  }

  /**
   * Get action button text based on order status
   */
  getActionButtonText(order: OrderHistory): string {
    switch (order.status) {
      case 0:
        return 'Chờ xác nhận';
      case 1:
        return 'Đã xác nhận';
      case 2:
        return 'Đang chuẩn bị';
      case 3:
        return 'Đang giao hàng';
      case 4:
        return 'Đã giao hàng';
      case 5:
        return 'Đã hủy';
      case 6:
        return 'Hoàn trả';
      case 7:
        return 'Chờ thanh toán';
      default:
        return 'Không xác định';
    }
  }

  /**
   * Get action button class based on order status
   */
  getActionButtonClass(order: OrderHistory): string {
    switch (order.status) {
      case 0:
        return 'pending-button';
      case 1:
        return 'confirmed-button';
      case 2:
        return 'preparing-button';
      case 3:
        return 'shipping-button';
      case 4:
        return 'delivered-button';
      case 5:
        return 'cancelled-button';
      case 6:
        return 'returned-button';
      case 7:
        return 'payment-pending-button';
      default:
        return 'unknown-button';
    }
  }

  /**
   * Track by function for ngFor optimization
   */
  trackByOrderId(_index: number, order: OrderHistory): string {
    return order._id;
  }

  /**
   * Get products preview text
   */
  getProductsPreview(products: any[]): string {
    if (!products || products.length === 0) return '';

    const firstProduct = products[0];
    const productName = firstProduct.name || firstProduct.info?.name || 'Sản phẩm';

    if (products.length === 1) {
      return productName;
    } else {
      return `${productName} và ${products.length - 1} sản phẩm khác`;
    }
  }
}
