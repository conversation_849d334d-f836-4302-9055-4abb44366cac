<!-- Pay Container -->
<div class="pay-container container">
  <!-- Cart Header -->
  <div class="pay-section-header w-100">
    <p class="pay-section-title">SẢN PHẨM TRONG GIỎ HÀNG</p>
  </div>

  <!-- Empty Cart Message -->
  <div *ngIf="!loading && (!cartItems || cartItems.length === 0)" class="empty-cart-container">
    <div class="empty-cart-content">
      <i-tabler name="shopping-cart" class="empty-cart-icon"></i-tabler>
      <p class="empty-cart-message">Giỏ hàng của bạn đang trống</p>
      <button class="shop-now-btn" routerLink="/cho-hai-san">
        <i-tabler name="shopping-bag"></i-tabler>
        Mua sắm ngay
      </button>
    </div>
  </div>

  <!-- Main Content -->
  <div *ngIf="!loading && cartItems && cartItems.length > 0" class="pay-content">
    <!-- Left Column -->
    <div class="left-section">
      <!-- Cart Items -->
      <div class="cart-items">
        <div class="cart-item" *ngFor="let item of cartItems; let i = index">
          <div class="item-container">
            <div class="item-header">
              <mat-checkbox class="item-checkbox" checked></mat-checkbox>
              <div class="item-image">
                <img
                  [src]="getProductImageUrl(item.info?.thumbail || '')"
                  [alt]="item.info?.name || 'Sản phẩm'"
                  (error)="handleImageError($event)">
              </div>
              <div class="item-details">
                <div class="item-name">{{ item.info?.name || 'Sản phẩm' }}</div>
                <div class="item-price">
                  <span class="current-price">{{ formatPrice(getItemPrice(item)) }}</span>
                  <span *ngIf="item.info && item.info.priceOld && item.info.priceOld > getItemPrice(item)" class="old-price">{{ formatPrice(getItemOldPrice(item)) }}</span>
                </div>
                <div *ngIf="item.classifyActive" class="item-option">{{ item.classifyActive.name }}: {{ item.classifyActive.value }}</div>
                <div class="item-quantity-section">
                  <span class="quantity-label">Số lượng</span>
                  <div class="quantity-controls">
                    <button
                      class="quantity-btn"
                      (click)="decreaseQuantity(i)"
                      [disabled]="item.count <= 1">
                      -
                    </button>
                    <span class="quantity-value">{{ item.count }}</span>
                    <button
                      class="quantity-btn"
                      (click)="increaseQuantity(i)">
                      +
                    </button>
                  </div>
                </div>
              </div>
              <div class="item-actions">
                <button class="remove-btn" (click)="removeItem(i)">✕</button>
              </div>
            </div>
            <div class="item-note">
              Ghi chú: {{ item.noteProduct || 'Không có' }}
            </div>
          </div>
        </div>
      </div>

      <!-- Store Selection Section -->
      <div class="selection-section">
        <h3 class="selection-title">Chọn cơ sở</h3>

        <!-- No stores message -->
        <div *ngIf="stores.length === 0" class="no-stores-message">
          <p>Không có cơ sở nào khả dụng.</p>
          <p>Vui lòng thử lại sau.</p>
        </div>

        <!-- Store selection -->
        <div *ngIf="stores.length > 0" class="selection-options vertical">
          <div class="store-option-item" *ngFor="let store of stores; let i = index">
            <input
              type="radio"
              [id]="'store-' + i"
              name="storeSelection"
              [checked]="selectedStoreIndex === i"
              (change)="selectedStoreIndex = i">
            <label [for]="'store-' + i" class="store-option-label">
              <!-- Store info -->
              <div class="store-info">
                <div class="store-name">
                  <i-tabler name="building-store" class="info-icon"></i-tabler>
                  {{ store.label }}
                </div>
                <div class="store-address">
                  <i-tabler name="map-pin" class="info-icon"></i-tabler>
                  {{ store.address }}
                </div>
                <div *ngIf="store.phone" class="store-phone">
                  <i-tabler name="phone" class="info-icon"></i-tabler>
                  {{ store.phone }}
                </div>
              </div>
            </label>
          </div>
        </div>
      </div>

      <!-- Customer Info Selection -->
      <div class="selection-section">
        <h3 class="selection-title">Chọn thông tin khách hàng</h3>

        <!-- No addresses message -->
        <div *ngIf="customerInfos.length === 0" class="no-addresses-message">
          <p>Chưa có địa chỉ giao hàng nào.</p>
          <p>Vui lòng thêm địa chỉ trong trang <a routerLink="/tai-khoan">Tài khoản</a>.</p>
        </div>

        <!-- Address selection -->
        <div *ngIf="customerInfos.length > 0" class="selection-options vertical">
          <div class="customer-option-item" *ngFor="let customer of customerInfos; let i = index">
            <input
              type="radio"
              [id]="'customer-' + i"
              name="customerSelection"
              [checked]="selectedCustomerIndex === i"
              (change)="selectedCustomerIndex = i">
            <label [for]="'customer-' + i" class="customer-option-label">
              <!-- Default badge -->
              <div *ngIf="customer.default" class="default-badge">
                <i-tabler name="check-circle" class="badge-icon"></i-tabler>
                Mặc định
              </div>

              <!-- Customer info -->
              <div class="customer-info">
                <div class="customer-name">
                  <i-tabler name="user" class="info-icon"></i-tabler>
                  {{ customer.name }}
                </div>
                <div class="customer-phone">
                  <i-tabler name="phone" class="info-icon"></i-tabler>
                  {{ customer.phone }}
                </div>
                <div class="customer-address">
                  <i-tabler name="map-pin" class="info-icon"></i-tabler>
                  {{ customer.address }}
                </div>
              </div>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Column -->
    <div class="right-section">
      <div class="right-panel">
        <!-- Coupon Input -->
        <div class="coupon-section">
          <div class="coupon-input">
            <input
              type="text"
              placeholder="Nhập mã giảm giá"
              [(ngModel)]="discountCode"
              class="coupon-field">
            <button
              class="coupon-btn"
              (click)="applyDiscountCode()">
              Áp dụng
            </button>
          </div>
        </div>

        <hr class="section-divider">

        <!-- Order Summary -->
        <div class="summary-section">
          <h3 class="summary-title">TỔNG TIỀN</h3>

          <div class="summary-item">
            <span class="summary-label">Số tiền</span>
            <span class="summary-value">{{ formatPrice(subtotal) }}</span>
          </div>

          <div class="summary-item">
            <span class="summary-label">Phí vận chuyển</span>
            <span class="summary-value">{{ formatPrice(0) }}</span>
          </div>

          <div class="summary-item" *ngIf="discountAmount > 0">
            <span class="summary-label">Mã giảm giá ({{ discountCode }})</span>
            <span class="summary-value discount">-{{ formatPrice(discountAmount) }}</span>
          </div>

          <div class="summary-item total">
            <span class="summary-label">Tổng tiền</span>
            <span class="summary-value">{{ formatPrice(totalAmount) }}</span>
          </div>
        </div>

        <hr class="section-divider">

        <!-- Payment Method Selection -->
        <div class="payment-section">
          <h3 class="payment-title">Phương thức thanh toán</h3>
          <div class="payment-options horizontal">
            <div class="payment-option" *ngFor="let method of paymentMethods; let i = index">
              <input
                type="radio"
                [id]="'payment-' + i"
                name="paymentMethod"
                [checked]="selectedPaymentIndex === i"
                (change)="selectedPaymentIndex = i">
              <label [for]="'payment-' + i" class="payment-label">
                <span class="payment-name">{{ method.name }}</span>
              </label>
            </div>
          </div>
        </div>

        <hr class="section-divider">

        <!-- Bank Selection - Only show when bank payment is selected -->
        <div class="bank-section" *ngIf="selectedPaymentIndex === 0">
          <h3 class="bank-title">Chọn ngân hàng</h3>
          <div class="bank-options wrap">
            <div class="bank-option" *ngFor="let bank of banks; let i = index">
              <input
                type="radio"
                [id]="'bank-' + i"
                name="bankSelection"
                [checked]="selectedBankIndex === i"
                (change)="selectedBankIndex = i">
              <label [for]="'bank-' + i" class="bank-label">
                <img [src]="bank.logo" [alt]="bank.name" class="bank-logo">
                <span class="bank-name">{{ bank.name }}</span>
              </label>
            </div>
          </div>
        </div>

        <hr class="section-divider">

        <!-- Order Button -->
        <button
          class="order-btn"
          (click)="processPayment()"
          [disabled]="loadingPayment || cartItems.length === 0">
          <span *ngIf="loadingPayment">Đang xử lý...</span>
          <span *ngIf="!loadingPayment">Thanh toán</span>
        </button>
      </div>
    </div>
  </div>
</div>
