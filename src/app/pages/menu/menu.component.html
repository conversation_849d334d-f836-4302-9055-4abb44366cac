<div class="menu-container container">
  <!-- Menu Content -->
  <div class="menu-content">
    <h1 class="menu-title">MENU</h1>
    <p class="note"><PERSON><PERSON> gần 200 món - Gi<PERSON> chưa bao gồm đồ uống và thuế VAT bắt buộc</p>
    <div class="category-buttons">
      <button [class.active]="selectedCategoryIndex === i" *ngFor="let item of listCategories; let i = index" (click)="changeCategory(i)">{{item.name}}</button>
    </div>

    <div class="buffet-showcase">
      <div class="buffet-card" *ngFor="let item of listProduct">
        <img [src]="imageApiUrl + item.thumbail || 'assets/images/placeholder.svg'"
             alt="{{ item.name }}"
             class="buffet-image"
             (error)="handleImageError($event)" />
        <h3 class="buffet-name">{{ item.name }}</h3>
        <p class="buffet-description">{{ item.description }}</p>

        <div class="d-flex flex-row align-items-center">
          <p class="price-label">Giá</p>
          <p class="buffet-price">

            {{ item.price | vndCurrency }}
          <p class="buffet-price-person">/người
          </p>
        </div>
      </div>
    </div>
    <button class="reserve-btn" (click)="onBookingClick()">ĐẶT BÀN NGAY</button>
  </div>

  <div class="separate"></div>

  <!-- Branches Section -->
  <div class="branches-section">
    <h2 class="branches-title">DANH SÁCH CƠ SỞ</h2>
    <div class="branch-list" *ngIf="!loadingBranches">
      <div class="branch-card" *ngFor="let branch of branches">
        <div class="branch-info">
          <div class="branch-name">
            <i-tabler name="home" class="branch-icon"></i-tabler>
            <span class="branch-text">{{ branch.name }}</span>
          </div>
          <div class="branch-address">
            <i-tabler name="map-pin" class="location-icon"></i-tabler>
            <span>{{ branch.address }}</span>
          </div>
          <div class="branch-phone">
            <i-tabler name="phone" class="phone-icon"></i-tabler>
            <span>{{ branch.phone }}</span>
          </div>
        </div>
        <button class="directions-btn" (click)="openGoogleMaps(branch)">Chỉ đường</button>
      </div>
    </div>
    <div *ngIf="loadingBranches" class="loading-message">
      Đang tải danh sách cơ sở...
    </div>
  </div>
</div>
