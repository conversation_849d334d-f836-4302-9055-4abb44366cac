import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { TablerIconsModule } from 'angular-tabler-icons';

export interface ConfirmDialogData {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'warning' | 'danger' | 'info';
}

@Component({
  selector: 'app-confirm-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    TablerIconsModule
  ],
  template: `
    <div class="confirm-dialog">
      <h2 mat-dialog-title class="d-flex align-items-center">
        <i-tabler 
          [name]="getIconName()" 
          [class]="getIconClass()"
          class="me-2">
        </i-tabler>
        {{ data.title }}
      </h2>

      <mat-dialog-content>
        <p class="dialog-message">{{ data.message }}</p>
      </mat-dialog-content>

      <mat-dialog-actions align="end">
        <button mat-button (click)="onCancel()">
          {{ data.cancelText || 'Hủy' }}
        </button>
        <button 
          mat-raised-button 
          [color]="getButtonColor()"
          (click)="onConfirm()">
          {{ data.confirmText || 'Xác nhận' }}
        </button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [`
    .confirm-dialog {
      min-width: 300px;
    }

    .dialog-message {
      margin: 16px 0;
      line-height: 1.5;
    }

    .icon-warning {
      color: #ff9800;
    }

    .icon-danger {
      color: #f44336;
    }

    .icon-info {
      color: #2196f3;
    }

    @media (max-width: 768px) {
      .confirm-dialog {
        min-width: unset;
        width: 100%;
      }
    }
  `]
})
export class ConfirmDialogComponent {
  constructor(
    private dialogRef: MatDialogRef<ConfirmDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDialogData
  ) {}

  /**
   * Get icon name based on dialog type
   */
  getIconName(): string {
    switch (this.data.type) {
      case 'warning':
        return 'alert-triangle';
      case 'danger':
        return 'alert-circle';
      case 'info':
        return 'info-circle';
      default:
        return 'help-circle';
    }
  }

  /**
   * Get icon CSS class based on dialog type
   */
  getIconClass(): string {
    switch (this.data.type) {
      case 'warning':
        return 'icon-warning';
      case 'danger':
        return 'icon-danger';
      case 'info':
        return 'icon-info';
      default:
        return 'icon-info';
    }
  }

  /**
   * Get button color based on dialog type
   */
  getButtonColor(): string {
    switch (this.data.type) {
      case 'warning':
        return 'accent';
      case 'danger':
        return 'warn';
      case 'info':
        return 'primary';
      default:
        return 'primary';
    }
  }

  /**
   * Handle confirm action
   */
  onConfirm(): void {
    this.dialogRef.close(true);
  }

  /**
   * Handle cancel action
   */
  onCancel(): void {
    this.dialogRef.close(false);
  }
}
