import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { LoadingDialogComponent } from './loading-dialog/loading-dialog.component';

@Injectable({
  providedIn: 'root',
})
export class LoadingService {
  private loadingSubject = new BehaviorSubject<boolean>(false);
  loading$ = this.loadingSubject.asObservable();
  private dialogRef: MatDialogRef<LoadingDialogComponent> | null = null;

  constructor(private dialog: MatDialog) {}

  /**
   * Shows the loading dialog
   */
  show() {
    if (!this.dialogRef) {
      this.dialogRef = this.dialog.open(LoadingDialogComponent, {
        disableClose: true, // Prevent closing by clicking outside
        hasBackdrop: true,
        backdropClass: 'loading-backdrop',
        panelClass: 'loading-panel',
      });
    }
    this.loadingSubject.next(true);
  }

  /**
   * Hides the loading dialog
   */
  hide() {
    if (this.dialogRef) {
      this.dialogRef.close();
      this.dialogRef = null;
    }
    this.loadingSubject.next(false);
  }

  /**
   * Returns the current loading state
   */
  isLoading(): boolean {
    return this.loadingSubject.value;
  }
}
