import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TablerIconsModule } from 'angular-tabler-icons';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { AuthService } from '../../core/services/auth.service';
import { Address } from '../../core/models/address.model';
import { AddressFormDialogComponent } from './address-form-dialog/address-form-dialog.component';
import { ConfirmDialogComponent } from '../confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-address-management',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    TablerIconsModule,
  ],
  template: `
    <mat-card class="address-management-card">
      <mat-card-header>
        <mat-card-title class="d-flex align-items-center">
          <i-tabler name="map-pin" class="me-2"></i-tabler>
          Quản lý địa chỉ
        </mat-card-title>
        <mat-card-subtitle>Quản lý danh sách địa chỉ giao hàng của bạn</mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <!-- Loading state -->
        <div *ngIf="loading" class="text-center py-4">
          <div class="spinner-border" role="status">
            <span class="visually-hidden">Đang tải...</span>
          </div>
        </div>

        <!-- Empty state -->
        <div *ngIf="!loading && addresses.length === 0" class="empty-state text-center py-5">
          <i-tabler name="map-pin-off" class="empty-icon mb-3"></i-tabler>
          <h5>Chưa có địa chỉ nào</h5>
          <p class="text-muted">Thêm địa chỉ đầu tiên để bắt đầu mua sắm</p>
          <button mat-raised-button color="primary" (click)="openAddDialog()">
            <i-tabler name="plus" class="me-1"></i-tabler>
            Thêm địa chỉ mới
          </button>
        </div>

        <!-- Address list -->
        <div *ngIf="!loading && addresses.length > 0" class="address-list">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <span class="text-muted">{{ addresses.length }} địa chỉ</span>
            <button mat-raised-button color="primary" (click)="openAddDialog()">
              <i-tabler name="plus" class="me-1"></i-tabler>
              Thêm địa chỉ mới
            </button>
          </div>

          <div class="row">
            <div
              *ngFor="let address of addresses; trackBy: trackByAddressId"
              class="col-12 col-md-6 mb-3"
            >
              <div class="address-card" [class.default-address]="address.default">
                <!-- Default badge -->
                <div *ngIf="address.default" class="default-badge">
                  <i-tabler name="check-circle" class="me-1"></i-tabler>
                  Mặc định
                </div>

                <!-- Address info -->
                <div class="address-info">
                  <h6 class="address-name">{{ address.name }}</h6>
                  <p class="address-phone">
                    <i-tabler name="phone" class="me-1"></i-tabler>
                    {{ address.phone }}
                  </p>
                  <p class="address-location">
                    <i-tabler name="map-pin" class="me-1"></i-tabler>
                    {{ address.address }}
                  </p>
                </div>

                <!-- Actions -->
                <div class="address-actions">
                  <button
                    *ngIf="!address.default"
                    mat-button
                    color="primary"
                    (click)="setDefaultAddress(address._id!)"
                    [disabled]="settingDefault"
                  >
                    Đặt làm mặc định
                  </button>

                  <button
                    mat-icon-button
                    color="primary"
                    (click)="openEditDialog(address)"
                    matTooltip="Chỉnh sửa"
                  >
                    <i-tabler name="edit"></i-tabler>
                  </button>

                  <button
                    mat-icon-button
                    color="warn"
                    (click)="confirmDelete(address)"
                    matTooltip="Xóa"
                    [disabled]="addresses.length === 1"
                  >
                    <i-tabler name="trash"></i-tabler>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  `,
  styles: [
    `
      .address-management-card {
        margin-top: 24px;
        margin-bottom: 24px;
      }

      .empty-state .empty-icon {
        font-size: 48px;
        color: #ccc;
      }

      .address-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        position: relative;
        transition: all 0.2s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .address-card:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .address-card.default-address {
        border-color: #4caf50;
        background-color: #f8fff8;
      }

      .default-badge {
        position: absolute;
        top: -8px;
        right: 8px;
        background: #4caf50;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        display: flex;
        align-items: center;
      }

      .address-info {
        flex: 1;
        margin-bottom: 16px;
      }

      .address-name {
        font-weight: 600;
        margin-bottom: 8px;
        color: #333;
      }

      .address-phone,
      .address-location {
        margin-bottom: 4px;
        color: #666;
        font-size: 14px;
        display: flex;
        align-items: flex-start;
      }

      .address-phone i-tabler,
      .address-location i-tabler {
        margin-top: 2px;
        flex-shrink: 0;
      }

      .address-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
      }

      .address-actions button[mat-button] {
        font-size: 12px;
      }

      @media (max-width: 768px) {
        .address-card {
          margin-bottom: 16px;
        }

        .address-actions {
          justify-content: space-between;
        }
      }
    `,
  ],
})
export class AddressManagementComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  addresses: Address[] = [];
  loading = false;
  settingDefault = false;

  constructor(
    private authService: AuthService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
  ) {}

  ngOnInit(): void {
    this.loadAddresses();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load addresses from service
   */
  loadAddresses(): void {
    this.loading = true;
    this.authService
      .getAddressList()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: addresses => {
          this.addresses = addresses;
          this.loading = false;
        },
        error: error => {
          console.error('Error loading addresses:', error);
          this.loading = false;
          this.snackBar.open('Có lỗi xảy ra khi tải danh sách địa chỉ', 'Đóng', {
            duration: 3000,
          });
        },
      });
  }

  /**
   * Open add address dialog
   */
  openAddDialog(): void {
    const dialogRef = this.dialog.open(AddressFormDialogComponent, {
      width: '600px',
      maxWidth: '90vw',
      data: { mode: 'add' },
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadAddresses();
      }
    });
  }

  /**
   * Open edit address dialog
   */
  openEditDialog(address: Address): void {
    const dialogRef = this.dialog.open(AddressFormDialogComponent, {
      width: '600px',
      maxWidth: '90vw',
      data: { mode: 'edit', address },
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadAddresses();
      }
    });
  }

  /**
   * Set address as default
   */
  async setDefaultAddress(addressId: string): Promise<void> {
    this.settingDefault = true;

    try {
      const result = await this.authService.setDefaultAddress(addressId);

      if (result.success) {
        this.snackBar.open(result.message, 'Đóng', { duration: 3000 });
        this.loadAddresses();
      } else {
        this.snackBar.open(result.message, 'Đóng', { duration: 3000 });
      }
    } catch (error) {
      console.error('Error setting default address:', error);
      this.snackBar.open('Có lỗi xảy ra', 'Đóng', { duration: 3000 });
    } finally {
      this.settingDefault = false;
    }
  }

  /**
   * Confirm delete address
   */
  confirmDelete(address: Address): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Xác nhận xóa',
        message: `Bạn có chắc chắn muốn xóa địa chỉ "${address.name}"?`,
        confirmText: 'Xóa',
        cancelText: 'Hủy',
      },
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && address._id) {
        this.deleteAddress(address._id);
      }
    });
  }

  /**
   * Delete address
   */
  async deleteAddress(addressId: string): Promise<void> {
    try {
      const result = await this.authService.deleteAddress(addressId);

      if (result.success) {
        this.snackBar.open(result.message, 'Đóng', { duration: 3000 });
        this.loadAddresses();
      } else {
        this.snackBar.open(result.message, 'Đóng', { duration: 3000 });
      }
    } catch (error) {
      console.error('Error deleting address:', error);
      this.snackBar.open('Có lỗi xảy ra khi xóa địa chỉ', 'Đóng', { duration: 3000 });
    }
  }

  /**
   * Track by function for ngFor
   */
  trackByAddressId(index: number, address: Address): string {
    return address._id || index.toString();
  }
}
