# Hướng Dẫn Sử Dụng Tính Năng Quản Lý Địa Chỉ

## Tổng quan
Tính năng quản lý địa chỉ cho phép người dùng:
- Xem danh sách địa chỉ giao hàng
- Thêm địa chỉ mới
- Chỉnh sửa địa chỉ hiện có
- Xóa địa chỉ không cần thiết
- Đặt địa chỉ mặc định

## Cấu trúc Files

### Models
- `src/app/core/models/address.model.ts` - Định nghĩa interfaces và helper functions
- `src/app/core/models/auth.model.ts` - Cập nhật UserModel với addressList

### Services
- `src/app/core/services/auth.service.ts` - Thêm các methods quản lý địa chỉ

### Components
- `src/app/components/address-management/address-management.component.ts` - Component chính
- `src/app/components/address-management/address-form-dialog/address-form-dialog.component.ts` - Dialog thêm/sửa
- `src/app/components/confirm-dialog/confirm-dialog.component.ts` - Dialog xác nhận

### Pages
- `src/app/pages/tai-khoan/tai-khoan.component.ts` - Tích hợp vào trang tài khoản

## API Endpoints

### 1. Lấy thông tin profile (bao gồm addressList)
```
GET /user/api/get-profile.html
Response: {
  error: false,
  data: {
    user: {
      _id: "string",
      fullName: "string",
      addressList: [
        {
          _id: "string",
          name: "string",
          phone: "string",
          province: "string",
          district: "string", 
          ward: "string",
          street: "string",
          address: "string",
          default: boolean
        }
      ]
    }
  }
}
```

### 2. Cập nhật profile (bao gồm addressList)
```
POST /user/api/update-profile.html
Body: {
  _id: "string",
  fullName: "string",
  addressList: [...]
}
Response: {
  error: false,
  message: "string",
  data: { token: "string" }
}
```

### 3. Lấy danh sách tỉnh/thành phố
```
GET /user/api/get-provinces
Response: {
  error: false,
  data: [
    {
      _id: "string",
      name: "string",
      code: "string"
    }
  ]
}
```

### 4. Lấy danh sách quận/huyện
```
GET /user/api/get-district-by-province?province={provinceCode}
Response: {
  error: false,
  data: [
    {
      _id: "string", 
      name: "string",
      code: "string",
      provinceCode: "string"
    }
  ]
}
```

### 5. Lấy danh sách phường/xã
```
GET /user/api/get-ward-by-district?district={districtCode}
Response: {
  error: false,
  data: [
    {
      _id: "string",
      name: "string", 
      code: "string",
      districtCode: "string"
    }
  ]
}
```

## Cách sử dụng

### 1. Import vào module
```typescript
import { AddressManagementComponent } from './components/address-management/address-management.component';

@Component({
  imports: [AddressManagementComponent]
})
```

### 2. Sử dụng trong template
```html
<app-address-management></app-address-management>
```

### 3. Sử dụng AuthService methods
```typescript
// Lấy danh sách địa chỉ
this.authService.getAddressList().subscribe(addresses => {
  console.log(addresses);
});

// Thêm địa chỉ mới
const addressData = {
  name: 'Nguyễn Văn A',
  phone: '0123456789',
  province: 'Hà Nội',
  district: 'Hai Bà Trưng',
  ward: 'Trường Định',
  street: 'Số 123 Trường Định',
  default: false
};

const result = await this.authService.addAddress(addressData);
if (result.success) {
  console.log('Thêm địa chỉ thành công');
}

// Cập nhật địa chỉ
const result = await this.authService.updateAddress(addressId, addressData);

// Xóa địa chỉ
const result = await this.authService.deleteAddress(addressId);

// Đặt địa chỉ mặc định
const result = await this.authService.setDefaultAddress(addressId);
```

## Validation Rules

### Tên (name)
- Bắt buộc
- Tối thiểu 2 ký tự
- Tối đa 100 ký tự

### Số điện thoại (phone)
- Bắt buộc
- Tối thiểu 10 ký tự
- Chỉ chấp nhận số, dấu +, -, (), khoảng trắng
- Tự động format +840 → +84

### Địa chỉ cụ thể (street)
- Bắt buộc
- Tối thiểu 5 ký tự
- Tối đa 200 ký tự

### Tỉnh/Quận/Phường
- Tất cả đều bắt buộc
- Phải chọn từ danh sách có sẵn

## Features

### 1. Responsive Design
- Hiển thị tốt trên desktop và mobile
- Grid layout tự động điều chỉnh
- Touch-friendly buttons

### 2. User Experience
- Loading states
- Error handling
- Success notifications
- Confirm dialogs
- Form validation

### 3. Data Management
- Tự động đặt địa chỉ đầu tiên làm mặc định
- Không cho phép xóa địa chỉ cuối cùng
- Tự động cập nhật địa chỉ mặc định khi xóa

### 4. Address Building
- Tự động tạo địa chỉ đầy đủ từ các thành phần
- Format số điện thoại
- Validation toàn diện

## Troubleshooting

### 1. Lỗi không tải được danh sách địa chỉ
- Kiểm tra user đã đăng nhập
- Kiểm tra API endpoint
- Kiểm tra token authentication

### 2. Lỗi không thêm được địa chỉ
- Kiểm tra validation form
- Kiểm tra API response
- Kiểm tra network connection

### 3. Lỗi không load được tỉnh/quận/phường
- Kiểm tra API endpoints
- Kiểm tra data format
- Fallback to mock data nếu cần

## Customization

### 1. Styling
Có thể customize CSS trong component styles:
```scss
.address-card {
  // Custom styles
}

.default-badge {
  // Custom badge styles
}
```

### 2. Validation
Có thể thay đổi validation rules trong `address.model.ts`:
```typescript
export const ADDRESS_VALIDATION = {
  NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 100
  }
  // ...
};
```

### 3. API Endpoints
Có thể thay đổi endpoints trong AuthService methods.

## Testing

### 1. Unit Tests
```typescript
describe('AddressManagementComponent', () => {
  it('should load addresses on init', () => {
    // Test implementation
  });
});
```

### 2. Integration Tests
- Test complete user flow
- Test API integration
- Test error scenarios

Tính năng quản lý địa chỉ đã sẵn sàng sử dụng và tích hợp hoàn toàn với logic mobile app!
